import os
import platform
import sys
import psutil
import socket
import getpass
from datetime import datetime
import subprocess


def get_system_info():
    """获取系统基本信息"""
    print("=" * 60)
    print("系统基本信息")
    print("=" * 60)

    print(f"操作系统: {platform.system()}")
    print(f"系统版本: {platform.release()}")
    print(f"系统详细版本: {platform.version()}")
    print(f"架构: {platform.architecture()[0]}")
    print(f"处理器: {platform.processor()}")
    print(f"机器类型: {platform.machine()}")
    print(f"网络名称: {platform.node()}")
    print(f"Python版本: {platform.python_version()}")
    print(f"Python实现: {platform.python_implementation()}")
    print()


def get_hardware_info():
    """获取硬件信息"""
    print("=" * 60)
    print("硬件信息")
    print("=" * 60)

    # CPU信息
    print(f"CPU核心数 (物理): {psutil.cpu_count(logical=False)}")
    print(f"CPU核心数 (逻辑): {psutil.cpu_count(logical=True)}")
    print(f"CPU使用率: {psutil.cpu_percent(interval=1)}%")

    # 内存信息
    memory = psutil.virtual_memory()
    print(f"总内存: {memory.total / (1024**3):.2f} GB")
    print(f"可用内存: {memory.available / (1024**3):.2f} GB")
    print(f"已用内存: {memory.used / (1024**3):.2f} GB")
    print(f"内存使用率: {memory.percent}%")

    # 磁盘信息
    print("\n磁盘信息:")
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            print(f"  设备: {partition.device}")
            print(f"  挂载点: {partition.mountpoint}")
            print(f"  文件系统: {partition.fstype}")
            print(f"  总空间: {usage.total / (1024**3):.2f} GB")
            print(f"  已用空间: {usage.used / (1024**3):.2f} GB")
            print(f"  可用空间: {usage.free / (1024**3):.2f} GB")
            print(f"  使用率: {(usage.used / usage.total) * 100:.1f}%")
            print()
        except PermissionError:
            print(f"  无法访问 {partition.device}")
            print()


def get_network_info():
    """获取网络信息"""
    print("=" * 60)
    print("网络信息")
    print("=" * 60)

    print(f"主机名: {socket.gethostname()}")
    print(f"IP地址: {socket.gethostbyname(socket.gethostname())}")

    # 网络接口信息
    print("\n网络接口:")
    for interface, addresses in psutil.net_if_addrs().items():
        print(f"  接口: {interface}")
        for addr in addresses:
            if addr.family == socket.AF_INET:
                print(f"    IPv4: {addr.address}")
            elif addr.family == socket.AF_INET6:
                print(f"    IPv6: {addr.address}")
        print()


def get_process_info():
    """获取进程信息"""
    print("=" * 60)
    print("进程信息")
    print("=" * 60)

    print(f"当前进程ID: {os.getpid()}")
    print(f"父进程ID: {os.getppid()}")
    print(f"当前用户: {getpass.getuser()}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"运行中的进程数: {len(psutil.pids())}")
    print()


def get_environment_info():
    """获取环境变量信息"""
    print("=" * 60)
    print("环境信息")
    print("=" * 60)

    print(f"系统启动时间: {datetime.fromtimestamp(psutil.boot_time())}")
    print(f"当前时间: {datetime.now()}")

    # 重要的环境变量
    important_vars = ['PATH', 'HOME', 'USER', 'SHELL', 'LANG', 'PYTHONPATH']
    print("\n重要环境变量:")
    for var in important_vars:
        value = os.environ.get(var, '未设置')
        if var == 'PATH':
            print(f"  {var}:")
            for path in value.split(os.pathsep)[:5]:  # 只显示前5个路径
                print(f"    {path}")
            if len(value.split(os.pathsep)) > 5:
                print(f"    ... 还有 {len(value.split(os.pathsep)) - 5} 个路径")
        else:
            print(f"  {var}: {value}")
    print()


def main():
    """主函数"""
    print("操作系统信息查看器")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    try:
        get_system_info()
        get_hardware_info()
        get_network_info()
        get_process_info()
        get_environment_info()

        print("=" * 60)
        print("信息收集完成!")
        print("=" * 60)

    except Exception as e:
        print(f"获取系统信息时出错: {e}")


if __name__ == "__main__":
    main()